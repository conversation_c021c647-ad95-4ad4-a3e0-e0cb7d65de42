
# Restaurant Operations Analytics Platform - Handoff Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Quick Start Guide](#quick-start-guide)
3. [Application Architecture](#application-architecture)
4. [Development Setup](#development-setup)
5. [Feature Documentation](#feature-documentation)
6. [Maintenance Guide](#maintenance-guide)
7. [Support Resources](#support-resources)

---

## Project Overview

### Executive Summary
The Restaurant Operations Analytics Platform is a comprehensive business intelligence tool designed for restaurant executives (CEOs, COOs, and management teams). It provides real-time performance monitoring, AI-driven insights, and actionable recommendations to optimize restaurant operations.

### Key Features
- **Real-time Dashboard**: Performance metrics, sales trends, and operational KPIs
- **AI Assistant (OpSage)**: Conversational analytics with contextual insights
- **Multi-location Analytics**: Performance comparison across restaurant locations
- **Interactive Charts**: Sales trends, cost analysis, and customer sentiment tracking
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### Technology Stack
- **Frontend**: React 18 with TypeScript
- **UI Framework**: Shadcn/UI components with Tailwind CSS
- **State Management**: TanStack React Query
- **Routing**: React Router DOM v6
- **Charts**: Recharts library
- **Build Tool**: Vite
- **Icons**: Lucide React

### Project Structure
```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Shadcn/UI base components
│   ├── ChatInterface.tsx
│   ├── SalesChart.tsx
│   └── ...
├── pages/               # Route components
│   ├── Index.tsx        # Main dashboard
│   ├── OpSageAssistant.tsx
│   └── Reports.tsx
├── lib/                 # Utility functions
└── App.tsx             # Main app component
```

---

## Quick Start Guide

### Prerequisites
- Node.js 18+ installed
- Package manager (npm, yarn, or bun)
- Modern web browser

### Installation & Running
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Accessing the Application
- **Development**: 
- **Main Dashboard**: `/` - Overview of all metrics and performance data
- **AI Assistant**: `/opsage` - Conversational analytics interface
- **Reports**: `/reports` - Detailed reporting section

---

## Application Architecture

### Component Hierarchy
```
App
├── BrowserRouter
├── SidebarProvider
│   ├── AppSidebar (Navigation)
│   └── Pages
│       ├── Index (Dashboard)
│       │   ├── MetricCard (6x)
│       │   ├── SalesChart
│       │   ├── CostChart
│       │   └── Various Analytics Components
│       ├── OpSageAssistant (AI Chat)
│       │   ├── ChatInterface
│       │   ├── SuggestedQuestions
│       │   └── Sidebar Metrics
│       └── Reports
```

### Data Flow
1. **Static Data**: Currently uses sample data for demonstration
2. **State Management**: React Query handles data fetching and caching
3. **Component Communication**: Props and context for state sharing
4. **AI Responses**: Generated based on conversation context and detected topics

### Key Design Patterns
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Component Composition**: Reusable components with clear interfaces
- **Type Safety**: Full TypeScript integration
- **Accessibility**: ARIA labels and keyboard navigation support

---

For detailed information on each section, please refer to the individual documentation files:
- [Development Setup Guide](./docs/DEVELOPMENT_SETUP.md)
- [Feature Documentation](./docs/FEATURES.md)
- [AI Assistant Guide](./docs/AI_ASSISTANT.md)
- [Maintenance Guide](./docs/MAINTENANCE.md)
- [API Integration Guide](./docs/API_INTEGRATION.md)
- [Security Guidelines](./docs/SECURITY.md)
- [Troubleshooting Guide](./docs/TROUBLESHOOTING.md)

---

## Contact & Support
For technical support or questions about this handoff documentation, please refer to the troubleshooting guide or contact the development team.

**Project Handoff Date**: December 12, 2025
**Documentation Version**: 1.0.0
