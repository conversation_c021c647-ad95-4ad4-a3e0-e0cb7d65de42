export const salesData = [
  { day: "Mon", actual: 4200, forecasted: 4500 },
  { day: "Tue", actual: 3800, forecasted: 4300 },
  { day: "Wed", actual: 4500, forecasted: 4600 },
  { day: "Thu", actual: 4800, forecasted: 5000 },
  { day: "Fri", actual: 5200, forecasted: 5400 },
  { day: "Sat", actual: 5500, forecasted: 5600 },
  { day: "Sun", actual: 5800, forecasted: 5800 },
];

export const storeLocations = [
  { name: "Jacksonville", performance: -25, status: "poor", region: "Northeast" },
  { name: "Gainesville", performance: -18, status: "poor", region: "North Central" },
  { name: "Orlando", performance: -15, status: "below_average", region: "Central" },
  { name: "Tampa", performance: -12, status: "below_average", region: "West Central" },
  { name: "Lakeland", performance: -8, status: "below_average", region: "Central" },
  { name: "Sarasota", performance: -5, status: "average", region: "Southwest" },
  { name: "Fort Myers", performance: -3, status: "average", region: "Southwest" },
  { name: "West Palm Beach", performance: 2, status: "average", region: "Southeast" },
  { name: "Miami", performance: 5, status: "above_average", region: "South" },
  { name: "Fort Lauderdale", performance: 8, status: "above_average", region: "Southeast" },
];

export const contextualQuestions = {
  initial: [
    "What are customers saying about this item?",
    "Is this underperforming everywhere or just certain locations?", 
    "What's the financial impact if we keep or remove this item?",
    "What changes could improve performance?"
  ],
  sentiment_analysis: [
    "What are the main customer complaints?",
    "How can we improve customer satisfaction?",
    "What would make customers order this again?",
    "Should we adjust pricing or portions?"
  ],
  general_response: [
    "Break down performance by location",
    "Show customer behavior patterns", 
    "Calculate financial recovery scenarios",
    "Generate action plan"
  ]
};

export const suggestedQuestions = [
  "What are customers saying about this item?",
  "Is this underperforming everywhere or just certain locations?", 
  "What's the financial impact if we keep or remove this item?",
  "What changes could improve performance?"
];
