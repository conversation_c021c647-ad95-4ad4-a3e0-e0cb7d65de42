
import React from "react";
import { Header } from "@/components/Header";
import { AppSidebar } from "@/components/Sidebar";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { DailySalesReport } from "@/components/DailySalesReport";

export default function Reports() {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <SidebarInset>
          <Header />
          <div className="flex-1 p-6 space-y-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-semibold text-sm">📊</span>
              </div>
              <h1 className="text-2xl font-semibold">Reports</h1>
            </div>

            <div className="grid grid-cols-1 gap-6">
              <DailySalesReport />
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
