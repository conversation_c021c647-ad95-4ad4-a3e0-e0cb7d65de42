
import React from "react";
import { Header } from "@/components/Header";
import { AppSidebar } from "@/components/Sidebar";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { ChatInterface } from "@/components/ChatInterface";
import { SuggestedQuestions } from "@/components/SuggestedQuestions";
import { useConversation } from "@/hooks/useConversation";

export default function OpSageAssistant() {
  const {
    message,
    setMessage,
    conversationStage,
    showSuggestedQuestions,
    messages,
    handleSendMessage,
    handleQuestionClick
  } = useConversation();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <SidebarInset>
          <Header />
          <div className="flex-1 p-6 space-y-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-semibold text-sm">AI</span>
              </div>
              <h1 className="text-2xl font-semibold">OpSage AI Assistant</h1>
            </div>

            {/* Full-width Chat Interface */}
            <div className="w-full space-y-4">
              <ChatInterface
                messages={messages}
                message={message}
                setMessage={setMessage}
                handleSendMessage={handleSendMessage}
              />

              {/* Show suggested questions only initially */}
              {showSuggestedQuestions && (
                <SuggestedQuestions 
                  setMessage={setMessage} 
                  handleSendMessage={handleQuestionClick}
                  conversationStage={conversationStage} 
                />
              )}
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
