
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const locations = [
  {
    location: "Location 1",
    issue: "High Labor Cost",
    status: "High Risk",
    statusColor: "bg-red-100 text-red-800"
  },
  {
    location: "Location 2",
    issue: "Low Customer Count",
    status: "Medium Risk",
    statusColor: "bg-yellow-100 text-yellow-800"
  },
  {
    location: "Location 2",
    issue: "Declining Sentiment",
    status: "Low Risk",
    statusColor: "bg-green-100 text-green-800"
  }
];

export function LocationsTable() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Locations at Risk</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-4 gap-4 text-sm font-medium text-muted-foreground border-b pb-2">
            <div>Location</div>
            <div>Issue</div>
            <div>Status</div>
            <div>Action</div>
          </div>
          {locations.map((location, index) => (
            <div key={index} className="grid grid-cols-4 gap-4 items-center text-sm">
              <div className="font-medium">{location.location}</div>
              <div className="text-muted-foreground">{location.issue}</div>
              <div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${location.statusColor}`}>
                  {location.status}
                </span>
              </div>
              <div>
                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-800">
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
