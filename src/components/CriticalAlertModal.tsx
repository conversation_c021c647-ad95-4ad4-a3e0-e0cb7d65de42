
import React from "react";
import {
  Di<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, TrendingDown, Users, Heart, Calendar } from "lucide-react";

interface CriticalAlertModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CriticalAlertModal({ open, onOpenChange }: CriticalAlertModalProps) {
  const handleAnalyzeIssue = () => {
    onOpenChange(false);
    window.location.href = "/opsage";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-0 overflow-hidden">
        {/* Header with Critical Alert */}
        <div className="bg-red-50 border-b border-red-200 p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-600">CRITICAL ALERT</span>
          </div>
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-foreground">
              Spicy Tuna Bowl
            </DialogTitle>
          </DialogHeader>
        </div>

        {/* Product Image */}
        <div className="px-4 pt-4">
          <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <div className="text-center text-gray-500">
              <span className="text-sm">Product Image</span>
            </div>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="px-4 pb-4">
          <div className="grid grid-cols-2 gap-3 mb-6">
            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 mb-1">
                <TrendingDown className="h-4 w-4 text-red-600" />
                <span className="text-xs text-red-600 font-medium">FORECAST</span>
              </div>
              <div className="text-lg font-bold text-red-600">-15%</div>
            </div>

            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs text-red-600 font-medium">REVENUE GAP</span>
              </div>
              <div className="text-lg font-bold text-red-600">$2,450</div>
            </div>

            <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2 mb-1">
                <Users className="h-4 w-4 text-orange-600" />
                <span className="text-xs text-orange-600 font-medium">REPEAT PURCHASES</span>
              </div>
              <div className="text-lg font-bold text-orange-600">-8%</div>
            </div>

            <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2 mb-1">
                <Heart className="h-4 w-4 text-orange-600" />
                <span className="text-xs text-orange-600 font-medium">CUSTOMER SENTIMENT</span>
              </div>
              <div className="text-lg font-bold text-orange-600">3.2/5</div>
            </div>
          </div>

          {/* Consecutive Days Alert */}
          <div className="bg-red-50 p-3 rounded-lg border border-red-200 mb-6">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-600">
                3 consecutive days of declining performance
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              onClick={handleAnalyzeIssue}
            >
              Analyze Issue
            </Button>
            <Button variant="outline" className="w-full border-blue-600 text-blue-600 hover:bg-blue-50">
              Track Performance
            </Button>
            <Button variant="outline" className="w-full border-gray-300 text-gray-700 hover:bg-gray-50">
              Notify Team
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
