
import { contextualQuestions } from "@/constants/data";

interface AIResponse {
  content: string;
  newStage: string;
}

// Enhanced contextual responses based on conversation flow
export const generateContextualResponse = (topic: string, userMessage: string, stage: string) => {
  const responses = {
    customer_sentiment: {
      content: "**Sentiment Reveal**\n\nPositive 22%\nNeutral 26%\nNegative 52%\n\nI've analyzed 230 customer reviews, social media mentions, and feedback cards for the Spicy Tuna Bowl. Here's what customers are saying:\n\n**INLINE_IMAGE:/lovable-uploads/7e70f58d-7245-4d97-a816-b939eed2342c.png**\n\nThe primary issue appears to be the sodium level in the dish, with portion size concerns as a secondary factor. These are consistent across locations but more pronounced in suburban stores.\n\n**Would you like to:**\n\n**CLICKABLE_QUESTIONS_CUSTOM**\n\nWould you like to see the key factors affecting performance or explore specific aspects of the data?\n\n**INLINE_IMAGE:/lovable-uploads/c5d661d5-c436-4a98-8611-fb9652c7cb49.png**",
      newStage: "sentiment_analysis"
    },
    performance: {
      content: "I've analyzed the Spicy Tuna Bowl performance across all locations. Here's the breakdown:\n\n**INLINE_METRICS:performance**\n\nThe underperformance is most severe in Jacksonville (-35%) and Gainesville (-22%), while Miami shows positive momentum (+15%). \n\n**Would you like me to:**\n\n**CLICKABLE_QUESTIONS**",
      newStage: "performance_analysis"
    },
    location: {
      content: "**Regional Performance Analysis**\n\n**INLINE_MAP:store_map**\n\nNorth Florida stores (Jacksonville, Gainesville) show the poorest performance, likely due to demographic preferences and competitive landscape. South Florida performs better with Miami leading at +15%.\n\n**Key Insights:**\n• Urban locations outperform suburban ones\n• Coastal areas show better reception\n• College towns struggle with the price point\n\nNext steps:\n\n**CLICKABLE_QUESTIONS**",
      newStage: "location_analysis"
    },
    financial_impact: {
      content: "**Financial Impact Analysis - Spicy Tuna Bowl LTO**\n\n**INLINE_METRICS:financial**\n\n**Scenario Modeling:**\n\n**If we keep the LTO as-is:**\n• Weekly loss: $2,450\n• 8-week campaign loss: $19,600\n• Opportunity cost vs. successful item: $35,000\n\n**If we remove it now:**\n• Immediate savings: $2,450/week\n• Menu slot available for proven performer\n• Potential revenue recovery: $15,000-25,000\n\n**If we optimize:**\n• Reduce sodium by 20%: Potential +8% performance\n• Adjust portion size: +$0.50 margin improvement\n• Regional pricing: +12% revenue in Miami/Orlando\n\n🔴 **Recommendation:** Remove LTO and replace with proven performer\n\n**CLICKABLE_QUESTIONS**",
      newStage: "financial_analysis"
    }
  };

  return responses[topic as keyof typeof responses] || responses.customer_sentiment;
};

export const generateAIResponse = (userMessage: string, currentStage: string): AIResponse => {
  console.log("Generating AI response for:", userMessage, "Stage:", currentStage);
  
  // Handle Yes/No responses for performance factors
  if (currentStage === "post_report" && userMessage.toLowerCase() === "yes") {
    return {
      content: "**INLINE_IMAGE:/lovable-uploads/35743fc2-2f83-42b4-9bcf-5fff9aeb8c56.png**\n\n**DELAY_FOLLOW_UP**",
      newStage: "showing_performance_factors"
    };
  }

  if (currentStage === "post_report" && userMessage.toLowerCase() === "no") {
    return {
      content: "",
      newStage: "conversation_ended"
    };
  }

  // Handle follow-up questions after showing performance factors
  if (currentStage === "showing_performance_factors") {
    if (userMessage.toLowerCase().includes("generate a report for your team")) {
      return {
        content: "**GENERATING_REPORT**\n\n⚡ **Creating Team Action Plan**\n\n📄 **Spicy Tuna Bowl - Action Plan & Assignments**\n\nGenerating comprehensive action plan with specific assignments for your team members...\n\n**Assignments Being Created:**\n• Kitchen Manager: Recipe modification guidelines\n• Marketing Lead: Regional messaging adjustments\n• Operations: Portion size optimization testing\n• Finance: Cost impact analysis and projections\n\n👁 **Report ready in your dashboard**\n\n**POST_REPORT_SUCCESS**",
        newStage: "report_completed"
      };
    }
    
    if (userMessage.toLowerCase().includes("explore any of these factors") || 
        userMessage.toLowerCase().includes("add performance tracking")) {
      return {
        content: "",
        newStage: "no_action_needed"
      };
    }
  }

  // Handle the completion message after report generation
  if (currentStage === "report_completed") {
    return {
      content: "Your action plan has been created and assignments have been sent to your team. I'll monitor the performance of the Spicy Tuna Bowl and provide a follow-up report in 3 days.\n\n**POST_COMPLETION_DELAY**",
      newStage: "awaiting_post_completion_questions"
    };
  }

  // Handle post-completion action responses
  if (currentStage === "post_completion_actions") {
    if (userMessage.toLowerCase().includes("add a daily performance tracker")) {
      return {
        content: "I've added a daily tracker for the Spicy Tuna Bowl to your dashboard and will prepare a case study documenting this situation and the results of your interventions.",
        newStage: "tracker_added"
      };
    }
    
    if (userMessage.toLowerCase().includes("schedule a team meeting")) {
      return {
        content: "I've scheduled a team meeting for tomorrow at 2 PM to review the Spicy Tuna Bowl results and discuss next steps. Calendar invites have been sent to all relevant stakeholders.",
        newStage: "meeting_scheduled"
      };
    }
    
    if (userMessage.toLowerCase().includes("share this analysis with regional managers")) {
      return {
        content: "I've compiled the analysis into a comprehensive report and shared it with all regional managers. They'll receive insights specific to their locations along with recommended action items.",
        newStage: "analysis_shared"
      };
    }
  }

  // Handle specific customer sentiment question
  if (userMessage.toLowerCase().includes("customers saying")) {
    return {
      content: "**Sentiment Reveal**\n\nPositive 22%\nNeutral 26%\nNegative 52%\n\nI've analyzed 230 customer reviews, social media mentions, and feedback cards for the Spicy Tuna Bowl. Here's what customers are saying:\n\n**INLINE_IMAGE:/lovable-uploads/7e70f58d-7245-4d97-a816-b939eed2342c.png**\n\nThe primary issue appears to be the sodium level in the dish, with portion size concerns as a secondary factor. These are consistent across locations but more pronounced in suburban stores.\n\n**Would you like to:**\n\n**CLICKABLE_QUESTIONS_CUSTOM**\n\nWould you like to see the key factors affecting performance or explore specific aspects of the data?\n\n**INLINE_IMAGE:/lovable-uploads/c5d661d5-c436-4a98-8611-fb9652c7cb49.png**",
      newStage: "sentiment_analysis"
    };
  }

  // Handle performance question
  if (userMessage.toLowerCase().includes("underperforming") || userMessage.toLowerCase().includes("performance")) {
    return generateContextualResponse("performance", userMessage, currentStage);
  }

  // Handle financial impact question
  if (userMessage.toLowerCase().includes("financial") || userMessage.toLowerCase().includes("impact") || userMessage.toLowerCase().includes("keep") || userMessage.toLowerCase().includes("remove")) {
    return generateContextualResponse("financial_impact", userMessage, currentStage);
  }

  // Handle location question
  if (userMessage.toLowerCase().includes("location") || userMessage.toLowerCase().includes("everywhere") || userMessage.toLowerCase().includes("certain locations")) {
    return generateContextualResponse("location", userMessage, currentStage);
  }

  // Handle sentiment analysis follow-up questions
  if (currentStage === "sentiment_analysis") {
    if (userMessage.toLowerCase().includes("location")) {
      return {
        content: "**Geographic Sentiment Breakdown**\n\n🔴 **Jacksonville:** 65% negative (\"too salty\", \"small portions\")\n🟡 **Gainesville:** 58% negative (\"overpriced for students\")\n🟡 **Tampa/Orlando:** 45% negative (\"okay but not great\")\n🔵 **Miami:** 35% negative (\"decent flavor, could be bigger\")\n\n**Regional Patterns:**\n• Northern FL: Price sensitivity + sodium complaints\n• Central FL: Mixed reactions, portion size issues\n• Southern FL: More accepting, mainly portion feedback\n\n**Recommendation:** Consider regional menu variations or pricing adjustments.\n\n**CLICKABLE_QUESTIONS**",
        newStage: "location_sentiment"
      };
    }
    
    if (userMessage.toLowerCase().includes("successful") || userMessage.toLowerCase().includes("compare")) {
      return {
        content: "**LTO Performance Comparison**\n\n**Spicy Tuna Bowl (Current):**\n• 22% positive sentiment\n• $12.99 price point\n• -15% vs forecast\n• 22% repeat purchase\n\n**Korean BBQ Bowl (Q2 Success):**\n• 78% positive sentiment\n• $11.99 price point\n• +25% vs forecast\n• 67% repeat purchase\n\n**Mediterranean Wrap (Q1 Success):**\n• 71% positive sentiment\n• $9.99 price point\n• +18% vs forecast\n• 54% repeat purchase\n\n**Key Success Factors:**\n✓ Price points under $12\n✓ Familiar flavor profiles\n✓ Generous portions\n✓ Instagram-worthy presentation\n\n**CLICKABLE_QUESTIONS**",
        newStage: "comparison_analysis"
      };
    }

    if (userMessage.toLowerCase().includes("recommendations") || userMessage.toLowerCase().includes("address")) {
      return {
        content: "**GENERATING_REPORT**\n\n⚡ **Generating Action Plan**\n\n📄 **Comprehensive Recommendations Report**\n\nAnalyzing customer feedback patterns, operational constraints, and financial impact to develop targeted solutions...\n\n**Report Sections:**\n• Recipe modifications to address sodium concerns\n• Portion size optimization strategies  \n• Regional pricing adjustments\n• Marketing message refinements\n• Timeline for implementation\n\n👁 **Report will be ready in your Reports dashboard in 2-3 minutes**\n\n**Immediate Actions You Can Take:**\n• Review kitchen prep guidelines for sodium reduction\n• Test portion size increase in Miami location\n• Pause digital marketing spend until optimizations complete\n\nWould you like to see the key factors affecting performance?\n\n**CLICKABLE_QUESTIONS_YES_NO**",
        newStage: "post_report"
      };
    }
  }

  // Default response with more contextual suggestions
  return {
    content: `I understand you're asking about "${userMessage}". Let me provide some insights:\n\n**INLINE_METRICS:lto_analysis**\n\nBased on the current data, the Spicy Tuna Bowl is significantly underperforming. Would you like me to dive deeper into any specific aspect?\n\n**CLICKABLE_QUESTIONS**`,
    newStage: "general_analysis"
  };
};

export const detectTopicFromMessage = (userMessage: string): string => {
  const lowerMessage = userMessage.toLowerCase();
  
  // Check for customer sentiment/feedback questions
  if (lowerMessage.includes("customers saying") || lowerMessage.includes("customer feedback") || lowerMessage.includes("sentiment")) {
    return "customer_sentiment";
  }
  
  // Performance/metrics keywords
  if (lowerMessage.match(/\b(performance|metrics?|factors?|breakdown|kpi|numbers?|data|analytics?|stats?)\b/)) {
    return "performance";
  }
  
  // Location/store keywords
  if (lowerMessage.match(/\b(location|store|where|city|region|geographic|map|area|site)\b/)) {
    return "location";
  }
  
  // Financial keywords
  if (lowerMessage.match(/\b(cost|money|revenue|profit|financial|price|pricing|budget|expense|roi|margin)\b/)) {
    return "financial";
  }
  
  // Competition keywords
  if (lowerMessage.match(/\b(compet\w*|market|benchmark|industry|rival|compare|comparison|versus|vs)\b/)) {
    return "competitive";
  }
  
  // Marketing keywords
  if (lowerMessage.match(/\b(marketing|advertis\w*|promotion|campaign|social|brand|messaging|awareness)\b/)) {
    return "marketing";
  }
  
  // Training/operational keywords
  if (lowerMessage.match(/\b(train\w*|staff|employee|execution|operation\w*|quality|consistency)\b/)) {
    return "training";
  }
  
  // Report/document keywords
  if (lowerMessage.match(/\b(report|document|executive|summary|generate|download|pdf)\b/)) {
    return "report";
  }
  
  // Recommendation/solution keywords
  if (lowerMessage.match(/\b(recommend\w*|suggest\w*|fix|solve|improve|action|next steps?|solution)\b/)) {
    return "recommendations";
  }
  
  return "general";
};
