
import React from "react";
import { MapPin } from "lucide-react";

const storeData = [
  { name: "Jacksonville Downtown", performance: -35, status: "poor", x: 85, y: 20 },
  { name: "Gainesville Campus", performance: -22, status: "poor", x: 75, y: 35 },
  { name: "Tampa Bay", performance: -8, status: "below_average", x: 50, y: 65 },
  { name: "Orlando Central", performance: +5, status: "above_average", x: 70, y: 55 },
  { name: "Miami Beach", performance: +15, status: "above_average", x: 85, y: 85 },
];

export const InlineStoreMap: React.FC = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "poor": return "#ef4444";
      case "below_average": return "#f97316";
      case "above_average": return "#10b981";
      default: return "#6b7280";
    }
  };

  return (
    <div className="w-full bg-white border rounded-lg p-4 my-3">
      <h3 className="text-sm font-semibold text-gray-800 mb-3">Florida Store Performance Map</h3>
      
      {/* Map Container */}
      <div className="relative w-full h-48 bg-gradient-to-b from-blue-50 to-green-50 rounded-lg border overflow-hidden">
        {/* Florida shape simulation */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100 via-transparent to-green-100 opacity-30"></div>
        
        {/* Store locations */}
        {storeData.map((store, index) => (
          <div
            key={index}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
            style={{ left: `${store.x}%`, top: `${store.y}%` }}
          >
            <div
              className="w-3 h-3 rounded-full border-2 border-white shadow-lg"
              style={{ backgroundColor: getStatusColor(store.status) }}
            />
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block z-10">
              <div className="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                <div className="font-medium">{store.name}</div>
                <div>{store.performance >= 0 ? '+' : ''}{store.performance}% vs forecast</div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Florida label */}
        <div className="absolute bottom-3 left-3 flex items-center gap-1">
          <MapPin className="h-4 w-4 text-blue-600" />
          <span className="text-xs font-medium text-blue-800">Florida (5 locations)</span>
        </div>
      </div>

      {/* Legend */}
      <div className="mt-3 grid grid-cols-3 gap-2 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 rounded-full bg-red-500"></div>
          <span>Poor (&lt;-15%)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
          <span>Below Avg</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 rounded-full bg-green-500"></div>
          <span>Above Avg</span>
        </div>
      </div>
    </div>
  );
};
