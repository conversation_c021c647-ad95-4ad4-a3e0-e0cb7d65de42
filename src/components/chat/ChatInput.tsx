
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send } from "lucide-react";

interface ChatInputProps {
  message: string;
  setMessage: (message: string) => void;
  handleSendMessage: () => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  message,
  setMessage,
  handleSendMessage
}) => {
  return (
    <div className="flex gap-2">
      <Input
        placeholder="Ask about the analysis..."
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        className="flex-1"
      />
      <Button onClick={handleSendMessage} size="icon">
        <Send className="h-4 w-4" />
      </Button>
    </div>
  );
};
