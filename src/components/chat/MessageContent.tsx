
import React from "react";
import { ClickableQuestions } from "./ClickableQuestions";
import { InlineImage } from "./InlineImage";
import { ReportStatus } from "./ReportStatus";
import { formatRegularContent } from "./ContentFormatter";
import { renderInlineComponent } from "./InlineComponentRenderer";

interface MessageContentProps {
  content: string;
}

export const MessageContent: React.FC<MessageContentProps> = ({ content }) => {
  const formatMessageContent = (content: string) => {
    // Handle embedded clickable questions first - they have their own image handling
    if (content.includes('**CLICKABLE_QUESTIONS**') || 
        content.includes('**CLICKABLE_QUESTIONS_CUSTOM**') ||
        content.includes('**CLICKABLE_QUESTIONS_YES_NO**') ||
        content.includes('**CLICKABLE_QUESTIONS_POST_COMPLETION**')) {
      
      const marker = content.includes('**CLICKABLE_QUESTIONS_CUSTOM**') ? '**CLICKABLE_QUESTIONS_CUSTOM**' :
                   content.includes('**CLICKABLE_QUESTIONS_YES_NO**') ? '**CLICKABLE_QUESTIONS_YES_NO**' :
                   content.includes('**CLICKABLE_QUESTIONS_POST_COMPLETION**') ? '**CLICKABLE_QUESTIONS_POST_COMPLETION**' :
                   '**CLICKABLE_QUESTIONS**';
      
      const parts = content.split(marker);
      const beforeQuestions = parts[0];
      const afterQuestions = parts[1] || '';
      
      return (
        <ClickableQuestions
          beforeQuestions={beforeQuestions}
          afterQuestions={afterQuestions}
          formatContent={formatRegularContent}
        />
      );
    }

    // Handle standalone inline images (not within clickable questions)
    if (content.includes('**INLINE_IMAGE:') && !content.includes('**CLICKABLE_QUESTIONS')) {
      const imageMatch = content.match(/\*\*INLINE_IMAGE:([^*]+)\*\*/);
      
      if (imageMatch) {
        const imagePath = imageMatch[1].trim();
        const beforeImage = content.split(imageMatch[0])[0];
        const afterImage = content.split(imageMatch[0])[1];
        
        return (
          <InlineImage
            imagePath={imagePath}
            beforeImage={beforeImage}
            afterImage={afterImage}
            formatContent={formatRegularContent}
          />
        );
      }
    }

    // Handle special report configuration format
    if (content.startsWith('**REPORT_CONFIG**')) {
      const cleanContent = content.replace('**REPORT_CONFIG**', '');
      return (
        <ReportStatus
          content={cleanContent}
          type="config"
          formatContent={formatRegularContent}
        />
      );
    }

    // Handle report generation status
    if (content.startsWith('**GENERATING_REPORT**')) {
      const cleanContent = content.replace('**GENERATING_REPORT**', '');
      return (
        <ReportStatus
          content={cleanContent}
          type="generating"
          formatContent={formatRegularContent}
        />
      );
    }

    // Handle inline charts and maps
    if (content.includes('**INLINE_CHART:') || content.includes('**INLINE_MAP:')) {
      const chartMatch = content.match(/\*\*INLINE_CHART:([^*]+)\*\*/);
      const mapMatch = content.match(/\*\*INLINE_MAP:([^*]+)\*\*/);
      
      if (chartMatch) {
        const chartType = chartMatch[1].trim();
        const beforeChart = content.split(chartMatch[0])[0];
        const afterChart = content.split(chartMatch[0])[1];
        
        return (
          <div className="space-y-3">
            {beforeChart && formatRegularContent(beforeChart)}
            {renderInlineComponent(chartType)}
            {afterChart && formatRegularContent(afterChart)}
          </div>
        );
      }
      
      if (mapMatch) {
        const mapType = mapMatch[1].trim();
        const beforeMap = content.split(mapMatch[0])[0];
        const afterMap = content.split(mapMatch[0])[1];
        
        return (
          <div className="space-y-3">
            {beforeMap && formatRegularContent(beforeMap)}
            {renderInlineComponent(mapType)}
            {afterMap && formatRegularContent(afterMap)}
          </div>
        );
      }
    }

    // Handle inline metrics
    if (content.includes('**INLINE_METRICS:')) {
      const parts = content.split('**INLINE_METRICS:');
      const beforeMetrics = parts[0];
      const metricsAndAfter = parts[1];
      const metricsMatch = metricsAndAfter.match(/^([^*]+)\*\*/);
      const metricsType = metricsMatch ? metricsMatch[1].trim() : '';
      const afterMetrics = metricsAndAfter.replace(/^[^*]+\*\*/, '');

      return (
        <div className="space-y-3">
          {beforeMetrics && formatRegularContent(beforeMetrics)}
          {renderInlineComponent(metricsType)}
          {afterMetrics && formatRegularContent(afterMetrics)}
        </div>
      );
    }

    return formatRegularContent(content);
  };

  return <>{formatMessageContent(content)}</>;
};
