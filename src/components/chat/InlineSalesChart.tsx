
import React from "react";
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

const salesData = [
  { day: "Mon", actual: 4200, forecasted: 4500 },
  { day: "Tue", actual: 3800, forecasted: 4300 },
  { day: "Wed", actual: 4500, forecasted: 4600 },
  { day: "Thu", actual: 4800, forecasted: 5000 },
  { day: "Fri", actual: 5200, forecasted: 5400 },
  { day: "Sat", actual: 5500, forecasted: 5600 },
  { day: "Sun", actual: 5800, forecasted: 5800 },
];

export const InlineSalesChart: React.FC = () => {
  return (
    <div className="w-full bg-white border rounded-lg p-4 my-3">
      <h3 className="text-sm font-semibold text-gray-800 mb-3">Daily Sales Performance - Spicy Tuna Bowl LTO</h3>
      <div className="h-48">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={salesData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="day" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: "#666" }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: "#666" }}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip 
              formatter={(value, name) => {
                const labels = {
                  actual: 'Actual Sales',
                  forecasted: 'Forecasted Sales'
                };
                return [`$${value}`, labels[name as keyof typeof labels] || name];
              }}
              labelStyle={{ color: "#666" }}
              contentStyle={{ 
                backgroundColor: "#fff", 
                border: "1px solid #e2e8f0",
                borderRadius: "6px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
              }}
            />
            <Line 
              type="monotone" 
              dataKey="actual" 
              stroke="#ef4444" 
              strokeWidth={3}
              dot={{ fill: "#ef4444", strokeWidth: 2, r: 4 }}
              name="actual"
            />
            <Line 
              type="monotone" 
              dataKey="forecasted" 
              stroke="#6b7280" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: "#6b7280", strokeWidth: 2, r: 3 }}
              name="forecasted"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="mt-3 flex justify-between text-sm text-gray-600">
        <span>Revenue Gap: $2,450/week</span>
        <span className="text-red-600">-15% vs Forecast</span>
      </div>
    </div>
  );
};
