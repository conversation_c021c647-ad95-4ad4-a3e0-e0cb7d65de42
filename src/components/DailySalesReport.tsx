
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts";

const dailySalesData = [
  { day: "Mon", current: 150, lastWeek: 180, yearAgo: 120 },
  { day: "Tue", current: 200, lastWeek: 220, yearAgo: 170 },
  { day: "Wed", current: 300, lastWeek: 320, yearAgo: 280 },
  { day: "Thu", current: 400, lastWeek: 450, yearAgo: 350 },
  { day: "Fri", current: 500, lastWeek: 520, yearAgo: 480 },
  { day: "Sat", current: 550, lastWeek: 580, yearAgo: 520 },
  { day: "Sun", current: 500, lastWeek: 540, yearAgo: 470 },
];

export const DailySalesReport: React.FC = () => {
  const currentWeek = dailySalesData.reduce((sum, day) => sum + day.current, 0);
  const lastWeek = dailySalesData.reduce((sum, day) => sum + day.lastWeek, 0);
  const yearAgo = dailySalesData.reduce((sum, day) => sum + day.yearAgo, 0);
  
  const weekOverWeekChange = ((currentWeek - lastWeek) / lastWeek * 100).toFixed(1);
  const yearOverYearChange = ((currentWeek - yearAgo) / yearAgo * 100).toFixed(1);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-blue-700">Daily Sales (Spicy Tuna Bowl)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dailySalesData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                dataKey="day" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
                domain={[0, 2000]}
                ticks={[0, 500, 1000, 1500, 2000]}
              />
              <Tooltip 
                formatter={(value, name) => {
                  const labels = {
                    current: "This Week",
                    lastWeek: "Last Week",
                    yearAgo: "Year Ago"
                  };
                  return [`${value}`, labels[name as keyof typeof labels] || name];
                }}
                labelStyle={{ color: "#666" }}
                contentStyle={{ 
                  backgroundColor: "#fff", 
                  border: "1px solid #e2e8f0",
                  borderRadius: "6px",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                }}
              />
              <Legend 
                verticalAlign="top" 
                height={36}
                iconType="line"
                wrapperStyle={{ paddingBottom: "15px" }}
              />
              <Line 
                type="monotone" 
                dataKey="current" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#3b82f6", strokeWidth: 2 }}
                name="This Week"
              />
              <Line 
                type="monotone" 
                dataKey="lastWeek" 
                stroke="#10b981" 
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: "#10b981", strokeWidth: 2, r: 3 }}
                activeDot={{ r: 5, stroke: "#10b981", strokeWidth: 2 }}
                name="Last Week"
              />
              <Line 
                type="monotone" 
                dataKey="yearAgo" 
                stroke="#8b5cf6" 
                strokeWidth={2}
                strokeDasharray="2 2"
                dot={{ fill: "#8b5cf6", strokeWidth: 2, r: 3 }}
                activeDot={{ r: 5, stroke: "#8b5cf6", strokeWidth: 2 }}
                name="Year Ago"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="text-sm">
              <div className="font-semibold text-blue-900">This Week</div>
              <div className="text-blue-700">Total: <span className="font-semibold">${currentWeek.toLocaleString()}</span></div>
              <div className="text-blue-600 text-xs">Today: $500</div>
            </div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <div className="text-sm">
              <div className="font-semibold text-green-900">vs Last Week</div>
              <div className="text-green-700">Total: <span className="font-semibold">${lastWeek.toLocaleString()}</span></div>
              <div className={`text-sm font-semibold ${parseFloat(weekOverWeekChange) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {parseFloat(weekOverWeekChange) >= 0 ? '+' : ''}{weekOverWeekChange}%
              </div>
            </div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
            <div className="text-sm">
              <div className="font-semibold text-purple-900">vs Year Ago</div>
              <div className="text-purple-700">Total: <span className="font-semibold">${yearAgo.toLocaleString()}</span></div>
              <div className={`text-sm font-semibold ${parseFloat(yearOverYearChange) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {parseFloat(yearOverYearChange) >= 0 ? '+' : ''}{yearOverYearChange}%
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
