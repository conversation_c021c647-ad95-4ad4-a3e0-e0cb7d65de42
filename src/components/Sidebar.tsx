
import { Home, Bar<PERSON>hart3, <PERSON>Tex<PERSON>, HelpCircle, User } from "lucide-react";
import {
  Sidebar,
  <PERSON>barContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

const menuItems = [
  {
    title: "Performance",
    icon: BarChart3,
    url: "/",
    active: false,
  },
  {
    title: "OpSage AI Assistant",
    icon: Home,
    url: "/opsage",
    active: false,
  },
  {
    title: "Reports",
    icon: FileText,
    url: "#",
    active: false,
  },
];

export function AppSidebar() {
  const currentPath = window.location.pathname;

  return (
    <Sidebar className="border-r border-border">
      <SidebarHeader className="p-4">
        <div className="flex items-center gap-3">
          <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.737 43C29.2062 43 35.2612 41.5872 35.2612 39.8444C35.2612 38.1015 29.2062 36.6887 21.737 36.6887C14.2679 36.6887 8.21289 38.1015 8.21289 39.8444C8.21289 41.5872 14.2679 43 21.737 43Z" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.21289 39.8444C8.21289 38.1163 14.2236 36.6887 21.737 36.6887C29.2505 36.6887 35.2612 38.1163 35.2612 39.8444" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.21286 34.5851C8.21286 32.857 14.2236 31.4294 21.737 31.4294C29.2504 31.4294 35.2612 32.857 35.2612 34.5851" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.21289 39.8443V20.6851" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M35.2612 39.8443V20.6851" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8.21288 22.4133C4.23077 22.4133 1 19.1825 1 15.2004C1 11.2183 4.23077 7.98755 8.21288 7.98755C9.71556 7.98755 11.068 8.43835 12.195 9.1897" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M31.2039 9.33984C32.331 8.5885 33.7585 8.1377 35.1861 8.1377C39.1682 8.1377 42.3989 11.3685 42.3989 15.3506C42.3989 19.3327 39.1682 22.5635 35.1861 22.5635" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M31.2791 10.542C31.2791 10.0912 31.2791 9.71556 31.2039 9.33989C30.6028 4.60644 26.6207 1 21.737 1C16.8533 1 12.8712 4.60644 12.2701 9.33989C12.195 9.71556 12.195 10.1664 12.195 10.542" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M29.3256 17.755C24.5921 15.2755 18.8819 15.2755 14.1485 17.755" stroke="#352BC2" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <div className="flex flex-col">
            <h1 className="text-lg font-semibold text-foreground">CONVX Ops</h1>
            <p className="text-sm text-muted-foreground">Operations management and monitoring</p>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    className={currentPath === item.url ? "bg-primary text-primary-foreground" : ""}
                  >
                    <a href={item.url} className="flex items-center gap-3">
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <div className="flex items-center gap-3 p-2">
                <HelpCircle className="h-4 w-4" />
                <span className="text-sm">Help & Support</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <div className="flex items-center gap-3 p-2">
                <User className="h-4 w-4" />
                <span className="text-sm">Mark</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
