
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function CustomerInsights() {
  return (
    <div className="grid grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Customer Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">1,230</div>
              <div className="text-sm text-muted-foreground">Total Customers</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Top 3 Issues from Surveys</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm">Wait Time</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Food Quality</span>
              <span className="text-sm font-medium">32%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Service</span>
              <span className="text-sm font-medium">23%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Active vs. Lapsing Customers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Active</span>
              <div className="flex items-center gap-2">
                <div className="w-20 h-2 bg-green-200 rounded-full">
                  <div className="w-16 h-2 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-sm font-medium">80%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Lapsing</span>
              <div className="flex items-center gap-2">
                <div className="w-20 h-2 bg-red-200 rounded-full">
                  <div className="w-4 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-sm font-medium">20%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
