
import { <PERSON>, Bell } from "lucide-react";
import { Input } from "@/components/ui/input";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { CriticalAlertModal } from "@/components/CriticalAlertModal";
import { useState } from "react";

export function Header() {
  const [alertModalOpen, setAlertModalOpen] = useState(false);

  return (
    <>
      <header className="flex items-center justify-between p-4 border-b border-border bg-background">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <div className="relative ml-8">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input 
              placeholder="Search..."
              className="pl-10 w-64"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">⌘</span>K
              </kbd>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAlertModalOpen(true)}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            Critical Alert
          </Button>
          <Bell className="h-5 w-5 text-muted-foreground cursor-pointer hover:text-foreground transition-colors" />
        </div>
      </header>

      <CriticalAlertModal 
        open={alertModalOpen} 
        onOpenChange={setAlertModalOpen} 
      />
    </>
  );
}
