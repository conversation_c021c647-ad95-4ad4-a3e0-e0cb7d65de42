
import { useState, useEffect } from "react";
import { generateAIResponse } from "@/components/chat/MessageHandler";

interface Message {
  type: "ai" | "user";
  content: string;
  timestamp: string;
}

export const useConversation = () => {
  const [message, setMessage] = useState("");
  const [conversationStage, setConversationStage] = useState("initial");
  const [discussedTopics, setDiscussedTopics] = useState<string[]>([]);
  const [showSuggestedQuestions, setShowSuggestedQuestions] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      type: "ai",
      content: "I've analyzed the performance of the Spicy Tuna Bowl LTO. Here's what I'm seeing:\n\n**INLINE_IMAGE:/lovable-uploads/17925edc-53d3-4884-8963-cc0f77e2fc2d.png**\n\nWhat would you like to know about the Spicy Tuna Bowl? You could ask:\n\n**CLICKABLE_QUESTIONS**",
      timestamp: "2:32 PM"
    }
  ]);

  // Add event listener for question clicks
  useEffect(() => {
    const handleQuestionClick = (event: CustomEvent) => {
      const question = event.detail.question;
      
      // Hide suggested questions immediately
      setShowSuggestedQuestions(false);
      
      // Add user message immediately
      setMessages(prev => [...prev, { 
        type: "user", 
        content: question, 
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      }]);
      
      // Clear input field
      setMessage("");
      
      // Generate AI response immediately
      setTimeout(() => {
        const response = generateAIResponse(question, conversationStage);
        
        setMessages(prev => [...prev, { 
          type: "ai", 
          content: response.content, 
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
        }]);
        setConversationStage(response.newStage);
        
        // Track discussed topics to avoid repetition
        const detectedTopic = question.toLowerCase().includes("customers saying") ? "customer_sentiment" : 
                             question.toLowerCase().includes("performance") ? "performance" : "general";
        if (!discussedTopics.includes(detectedTopic)) {
          setDiscussedTopics(prev => [...prev, detectedTopic]);
        }
      }, 1500);
    };

    // Add event listener for follow-up message trigger
    const handleFollowUpMessage = () => {
      setTimeout(() => {
        const response = generateAIResponse("", "report_completed");
        setMessages(prev => [...prev, { 
          type: "ai", 
          content: response.content, 
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
        }]);
        setConversationStage(response.newStage);
      }, 1000);
    };

    window.addEventListener('questionClick', handleQuestionClick as EventListener);
    window.addEventListener('triggerFollowUpMessage', handleFollowUpMessage);
    
    return () => {
      window.removeEventListener('questionClick', handleQuestionClick as EventListener);
      window.removeEventListener('triggerFollowUpMessage', handleFollowUpMessage);
    };
  }, [conversationStage, discussedTopics]);

  const handleSendMessage = () => {
    if (message.trim()) {
      // Hide suggested questions once user starts interacting
      setShowSuggestedQuestions(false);
      
      setMessages([...messages, { type: "user", content: message, timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) }]);
      
      // Generate AI response
      setTimeout(() => {
        const response = generateAIResponse(message, conversationStage);
        
        setMessages(prev => [...prev, { 
          type: "ai", 
          content: response.content, 
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
        }]);
        setConversationStage(response.newStage);
        
        // Track discussed topics to avoid repetition
        const detectedTopic = message.toLowerCase().includes("customers saying") ? "customer_sentiment" : 
                             message.toLowerCase().includes("performance") ? "performance" : "general";
        if (!discussedTopics.includes(detectedTopic)) {
          setDiscussedTopics(prev => [...prev, detectedTopic]);
        }
      }, 1500);
      
      setMessage("");
    }
  };

  const handleQuestionClick = (question: string) => {
    // This function is kept for compatibility but the main logic is now in the event listener
    const event = new CustomEvent('questionClick', { 
      detail: { question }
    });
    window.dispatchEvent(event);
  };

  return {
    message,
    setMessage,
    conversationStage,
    discussedTopics,
    showSuggestedQuestions,
    messages,
    handleSendMessage,
    handleQuestionClick
  };
};
