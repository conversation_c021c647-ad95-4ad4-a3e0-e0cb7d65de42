
# AI Assistant (OpSage) Guide

## Overview
OpSage is the AI-powered conversational analytics engine that provides intelligent insights and recommendations for restaurant operations. It analyzes user queries and generates contextual responses with relevant data visualizations.

## Core Architecture

### Conversation Flow
```typescript
// Main conversation stages
type ConversationStage = 
  | "initial"              // First interaction
  | "performance_analysis" // Performance deep dive
  | "location_analysis"    // Location-specific insights
  | "financial_analysis"   // Financial impact analysis
  | "competitive_analysis" // Market comparison
  | "marketing_analysis"   // Marketing performance
  | "training_analysis"    // Operational training
  | "customer_analysis"    // Customer feedback analysis
  | "recommendations_provided" // Action recommendations
  | "report_config"        // Report generation setup
  | "report_generated"     // Report completion
```

### Topic Detection Algorithm
The AI uses keyword matching to detect user intent:

```typescript
const detectTopicFromMessage = (userMessage: string): string => {
  const lowerMessage = userMessage.toLowerCase();
  
  // Performance/metrics keywords
  if (lowerMessage.match(/\b(performance|metrics?|factors?|breakdown|kpi|numbers?|data|analytics?|stats?)\b/)) {
    return "performance";
  }
  
  // Location/store keywords
  if (lowerMessage.match(/\b(location|store|where|city|region|geographic|map|area|site)\b/)) {
    return "location";
  }
  
  // Additional topic detection patterns...
}
```

## Response Generation System

### Contextual Response Types

#### 1. Performance Analysis
**Trigger Keywords**: performance, metrics, KPIs, data, analytics
**Response Includes**:
- Inline metrics visualization
- Key performance indicators breakdown
- Trend analysis with variance explanations
- Specific underperforming areas identification

**Sample Response Structure**:
```
Performance Breakdown for [Product/Location]:
**INLINE_METRICS:performance**
- Overall Rating: X/5 stars (Target: 4.0+)
- Customer Satisfaction: X% (Down X% from forecast)
- Repeat Purchase Rate: X% (Industry avg: X%)
- Net Promoter Score: X (Status indicator)
```

#### 2. Location Analysis
**Trigger Keywords**: location, store, geographic, area, site
**Response Includes**:
- Location comparison matrix
- Geographic performance variations
- Regional insight patterns
- Operational variance analysis

#### 3. Financial Analysis
**Trigger Keywords**: cost, revenue, profit, pricing, financial
**Response Includes**:
- Cost structure breakdown
- Revenue impact calculations
- Investment recovery projections
- Location-specific financial performance

#### 4. Report Generation
**Trigger Keywords**: report, document, executive, summary
**Response Includes**:
- Report configuration interface
- Progress tracking visualization
- Download/preview options
- Executive summary highlights

### Inline Metrics Integration

The AI assistant integrates visual metrics directly into responses using the `InlineMetrics` component:

```typescript
// Inline metrics are triggered by special markers in AI responses
**INLINE_METRICS:performance**
// This renders a visual metric card with relevant KPIs
```

**Metric Types**:
- `performance`: Overall performance indicators
- `financial`: Financial impact metrics
- `location`: Location-specific data
- Custom metrics based on context

## Message Formatting System

### Structured Content Recognition
The ChatInterface component recognizes and formats various content types:

#### Headers and Sections
```markdown
**Bold Headers** → Rendered as bold section headers
• Bullet points → Formatted as indented lists
☑ Checkboxes → Formatted as interactive checkboxes
✓ Progress items → Styled as green completion indicators
```

#### Data Points and Values
```markdown
Revenue: $47,200 → Highlighted in gray background
"Customer quotes" → Blue background with left border
#HashtagContent → Blue text styling
📄 File indicators → Gray background formatting
```

#### Special Formatting
- **Report Configuration**: Blue bordered sections
- **Report Generation**: Green bordered progress sections
- **Customer Quotes**: Italic text with blue accent border
- **Data Points**: Gray background highlight for metrics

## Conversation Memory

### Topic Tracking
```typescript
const [discussedTopics, setDiscussedTopics] = useState<string[]>([]);

// Prevents repetition of previously discussed topics
if (!discussedTopics.includes(detectedTopic)) {
  setDiscussedTopics(prev => [...prev, detectedTopic]);
}
```

### Stage Management
The conversation stage determines:
- Available suggested questions
- Response depth and detail
- Follow-up recommendations
- Report generation readiness

## Suggested Questions System

### Context-Aware Suggestions
Questions adapt based on:
- Current conversation stage
- Previously discussed topics
- Available data insights
- User interaction patterns

### Question Categories
- **Performance Deep Dives**: "What factors are affecting performance?"
- **Location Analysis**: "Which locations need immediate attention?"
- **Financial Impact**: "What's the revenue impact of these issues?"
- **Action Items**: "What specific actions should we take?"
- **Competitive Analysis**: "How do we compare to competitors?"

## Customization Guide

### Adding New Topics
1. **Update Topic Detection**: Add new keyword patterns to `detectTopicFromMessage`
2. **Create Response Template**: Add response structure to `generateContextualResponse`
3. **Design Inline Metrics**: Create new metric visualizations in `InlineMetrics`
4. **Update Suggested Questions**: Add topic-specific questions to `SuggestedQuestions`

### Modifying Response Templates
Response templates are defined in `generateContextualResponse`:

```typescript
const responses = {
  newTopic: {
    content: "Response template with **INLINE_METRICS:newTopic** integration",
    newStage: "new_analysis_stage"
  }
}
```

### Adding New Metric Types
1. **Create Metric Definition**: Define in `InlineMetrics` component
2. **Add Rendering Logic**: Include in `renderInlineMetrics` function
3. **Style Visualization**: Add appropriate color coding and layout
4. **Integrate Data Source**: Connect to relevant data structures

## Performance Considerations

### Response Generation
- Responses are generated with a 1.5-second delay to simulate processing
- Topic detection uses efficient regex matching
- Memory management prevents conversation state bloat

### Rendering Optimization
- Message formatting is memoized for performance
- Inline metrics use conditional rendering
- Chart components are lazy-loaded when needed

### Error Handling
- Graceful fallbacks for unrecognized topics
- Default responses for edge cases
- Conversation state recovery mechanisms

## Future Enhancement Opportunities

### Advanced AI Capabilities
- Natural language processing for more nuanced understanding
- Sentiment analysis of user queries
- Predictive question suggestions
- Multi-turn conversation context

### Data Integration
- Real-time data connections for live insights
- Historical trend analysis with actual data
- Automated alert generation based on thresholds
- Custom KPI tracking and reporting

### User Personalization
- User role-based response customization
- Preferred metric tracking
- Conversation history persistence
- Custom report templates
