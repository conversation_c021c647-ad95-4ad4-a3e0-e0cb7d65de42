
# Feature Documentation

## Overview
The Restaurant Operations Analytics Platform provides comprehensive business intelligence through three main interfaces and multiple analytical components.

## Main Navigation Pages

### 1. Performance Dashboard (`/`)
**Purpose**: Main overview of restaurant performance across all key metrics

**Key Components**:
- **Metric Cards Grid**: 6 primary KPIs displayed prominently
  - Total Sales: `$42,500` with week-over-week comparison
  - Average Check Size: `$12.45`
  - Customer Count: `1,230` total customers
  - Labor Cost %: `24.3%` of sales
  - Food Cost %: `30.2%` of sales
  - Customer Sentiment: `4.2/5` rating

- **Sales Chart**: Weekly sales trend comparison
  - Current week vs last week vs year ago
  - Interactive tooltips with detailed values
  - Responsive design adapts to screen size

- **Cost Chart**: Cost analysis and trends
  - Labor and food cost percentages
  - Historical comparison data

- **Insights Section**: Key business insights and alerts
- **Locations Table**: Performance breakdown by location
- **Customer Insights**: Sentiment and feedback analysis

### 2. OpSage AI Assistant (`/opsage`)
**Purpose**: Conversational analytics interface for deep-dive analysis

**Core Functionality**:
- **Intelligent Conversation**: Context-aware responses based on user queries
- **Topic Detection**: Automatically identifies query intent (performance, financial, location, etc.)
- **Dynamic Responses**: Generates detailed analysis based on detected topics
- **Visual Data Integration**: Inline metrics and charts within chat responses

**AI Capabilities**:
- Performance analysis with detailed breakdowns
- Location-specific insights and comparisons
- Financial impact analysis and projections
- Competitive analysis and benchmarking
- Marketing performance evaluation
- Operational training recommendations
- Executive report generation

**Interactive Features**:
- **Suggested Questions**: Context-aware question suggestions
- **Inline Metrics**: Visual data cards within responses
- **Report Generation**: AI-generated executive reports
- **Real-time Analysis**: Immediate insights based on current data

### 3. Reports (`/reports`)
**Purpose**: Detailed reporting interface for comprehensive analysis

## Component Library

### Charts and Visualizations

#### SalesChart Component
```typescript
// Located: src/components/SalesChart.tsx
// Purpose: Daily sales trend analysis
// Data: 7-day comparison (current vs last week vs year ago)
// Features: Interactive tooltips, legend, responsive design
```

#### SalesPerformanceChart Component
```typescript
// Located: src/components/SalesPerformanceChart.tsx
// Purpose: Sales performance with forecasting
// Data: Actual vs forecasted vs historical comparison
// Features: Multiple line types, change indicators
```

#### DailySalesReport Component
```typescript
// Located: src/components/DailySalesReport.tsx
// Purpose: Focused daily sales analysis for specific products
// Data: Spicy Tuna Bowl performance tracking
// Features: Product-specific insights, comparative analysis
```

### Interactive Components

#### ChatInterface Component
```typescript
// Located: src/components/ChatInterface.tsx
// Purpose: AI conversation interface
// Features:
// - Message formatting with structured data
// - Inline metrics rendering
// - Report generation status
// - Interactive input handling
```

#### InlineMetrics Component
```typescript
// Located: src/components/InlineMetrics.tsx
// Purpose: Visual metric cards within AI responses
// Features:
// - Color-coded metric display
// - Alert integration
// - Responsive grid layout
```

#### SuggestedQuestions Component
```typescript
// Located: src/components/SuggestedQuestions.tsx
// Purpose: Context-aware question suggestions
// Features:
// - Conversation stage awareness
// - Topic-based suggestions
// - Easy one-click questioning
```

### UI Components

#### MetricCard Component
```typescript
// Located: src/components/MetricCard.tsx
// Purpose: Key performance indicator display
// Features:
// - Trend indicators (up/down arrows)
// - Color-coded values
// - Subtitle context
```

#### Sidebar Navigation
```typescript
// Located: src/components/Sidebar.tsx
// Purpose: Application navigation
// Features:
// - Multi-level navigation
// - Active state management
// - Responsive collapse
```

## Data Structure

### Sample Data Format
```typescript
// Sales Data Structure
interface SalesDataPoint {
  day: string;           // "Mon", "Tue", etc.
  current: number;       // Current week sales
  lastWeek: number;      // Previous week comparison
  yearAgo: number;       // Year-over-year comparison
}

// Metric Card Data
interface MetricData {
  title: string;         // "TOTAL SALES"
  value: string;         // "$42,500"
  subtitle: string;      // Comparison context
  trend: "up" | "down";  // Trend direction
  trendValue: string;    // "(-5.6%)"
  color: string;         // Theme color
}
```

## AI Assistant Features

### Topic Detection
The AI assistant automatically detects user intent based on keywords:
- **Performance**: metrics, KPIs, data, analytics
- **Location**: store, geographic, area, site
- **Financial**: cost, revenue, profit, pricing
- **Competitive**: market, benchmark, comparison
- **Marketing**: advertising, campaigns, brand
- **Training**: staff, execution, quality
- **Customer**: feedback, sentiment, satisfaction

### Response Generation
Based on detected topics, the AI generates:
- Detailed analytical breakdowns
- Visual metric cards
- Actionable recommendations
- Comparative analysis
- Executive summaries

### Conversation Memory
- Tracks conversation stage and context
- Avoids repeating discussed topics
- Builds on previous responses
- Maintains topic awareness

## Integration Points

### Future Data Connections
The application is designed to integrate with:
- **POS Systems**: Real-time sales data
- **Inventory Management**: Stock levels and costs
- **Staff Management**: Labor costs and scheduling
- **Customer Reviews**: Sentiment analysis from multiple platforms
- **Weather APIs**: External factor correlation
- **Social Media**: Brand mention tracking

### API Integration Framework
- Modular design for easy integration
- Error handling and retry logic
- Data transformation utilities
- Real-time update capabilities

## Performance Features

### Optimization
- Lazy loading of chart components
- Memoized calculations for metric cards
- Efficient re-rendering with React Query
- Responsive image optimization

### Caching Strategy
- TanStack React Query for data caching
- Browser localStorage for user preferences
- Component-level memoization for expensive calculations

## Accessibility Features
- ARIA labels for screen readers
- Keyboard navigation support
- High contrast color schemes
- Responsive font sizing
- Focus management for interactive elements
